import { stringify } from 'qs';
import { useConfig } from '@/hooks/useConfig';
import { createWrappedApiClient } from '@/services/api-wrapper';
import { debug } from 'console';

const { serverConfig } = useConfig();

const apiClientInstance = createWrappedApiClient({
    baseURL: `${serverConfig.TURBINE_API_BASE_URL}/turbine/v1`,
    headers: {
        'Content-Type': 'application/json',
    },
    tokenKey: 'console_token',
});

// 获取所有应用 GET /api/v1/apps/all
export const getAllAppsApi = async () => {
    return apiClientInstance.get('/apps/all');
};

// 获取指定应用 GET /api/v1/apps/{id}
export const getAppByIdApi = async (id) => {
    return apiClientInstance.get(`/apps/${id}`);
};

// 获取话题列表 GET /api/v1/topics?app_id=turbine.blade_design&page=1&count=20
export const getTopicsApi = async (params = {}) => {
    // 构建查询参数
    const queryParams = {};

    // 添加筛选参数
    if (params.app_id) queryParams.app_id = params.app_id;

    // 添加分页参数
    queryParams.page = params.page || 1;
    queryParams.count = params.count || 100;

    // 将查询参数转换为URL查询字符串
    const queryString = stringify(queryParams);

    return apiClientInstance.get(`/topics?${queryString}`);
};

// 更新话题 PATCH /api/v1/topics/{id}
export const updateTopicApi = async (id, data) => {
    return apiClientInstance.patch(`/topics/${id}`, data);
};

// 删除话题 DELETE /api/v1/topics/{id}
export const deleteTopicApi = async (id) => {
    return apiClientInstance.delete(`/topics/${id}`);
};

// 获取消息列表 GET /api/v1/messages?topic_id=172&page=1&page_size=20
export const getMessagesApi = async (params = {}) => {
    // 构建查询参数
    const queryParams = {};

    // 添加筛选参数
    if (params.topic_id) queryParams.topic_id = params.topic_id;

    // 添加分页参数
    queryParams.page = params.page || 1;
    queryParams.page_size = params.page_size || 100;

    // 将查询参数转换为URL查询字符串
    const queryString = stringify(queryParams);

    return apiClientInstance.get(`/messages?${queryString}`);
};

// 删除消息 DELETE /api/v1/messages/{id}?topic_id=152
export const deleteMessageApi = async (id, topic_id) => {
    // 构建查询参数
    const queryParams = {};
    if (topic_id) queryParams.topic_id = topic_id;

    // 将查询参数转换为URL查询字符串
    const queryString = stringify(queryParams);

    return apiClientInstance.delete(`/messages/${id}?${queryString}`);
};

// 获取叶片工艺路线 GET /api/v1/blades/{id}/processes
export const getBladesProcessesApi = async (id) => {
    return apiClientInstance.get(`/blades/${id}/processes`);
};

// 批量获取叶片工序文件 GET /api/v1/blades/{id}/files
export const getBladesFilesApi = async (id) => {
    return apiClientInstance.get(`/blades/${id}/files`);
};

export const sendTaskmessageApi = async (
    content,
    app_id,
    topic_id,
    scene_token,
    abortController = null
) => {
    const token = localStorage.getItem('console_token');
    const formData = new FormData();
    formData.append('input_content', content);
    if (topic_id) {
        formData.append('topic_id', topic_id);
    }

    if (app_id) {
        formData.append('app_id', app_id);
    }

    // if (scene_token) {
    //     formData.append('scene_token', scene_token);
    // }

    const fetchOptions = {
        method: 'POST',
        headers: {
            Authorization: `Bearer ${token}`,
        },
        body: formData,
        timeout: 0, //永不超时
    };

    // 如果提供了AbortController，添加signal
    if (abortController) {
        fetchOptions.signal = abortController.signal;
    }

    const response = await fetch(
        `${serverConfig.TURBINE_API_BASE_URL}/turbine/v1/messages`,
        fetchOptions
    );

    if (!response.ok) {
        throw new Error('Network response was not ok');
    }
    return response.body;
};

// 上传数据包文件
export const fetchUploadDataPackage = async (file_zip) => {
    return apiClientInstance.post(
        `/data_packages`,
        { file_zip },
        {
            timeout: 0, //永不超时
            headers: { 'Content-Type': 'multipart/form-data' },
        }
    );
};

// 修改数据包机组型号、气缸编号、气缸名称、起始级号，
export const fetchUpdateDataPackage = async (id, post_data) => {
    return apiClientInstance.request(
        'PATCH',
        `/data_packages/${id}`,
        post_data // 第三个参数应该是params对象，这里不需要URL参数
    );
};

// 上传数据包数据到TC
export const fetchUploadDataPackageToTC = async (id, post_data) => {
    return apiClientInstance.post(`/data_packages/${id}/upload`, post_data);
};

// 删除数据包
export const fetchDeleteDataPackage = async (id) => {
    // return { data: { error: '0' } };
    return apiClientInstance.delete(`/data_packages/${id}`);
};

// 获取数据包详情
export const fetchGetDataPackageDetail = async (id) => {
    return apiClientInstance.get(`/data_packages/${id}`, {
        timeout: 0, //永不超时
    });
};

// 下载数据包 /data_packages/{id}/download  文件流格式返回
export const fetchDownloadDataPackage = async (id) => {
    try {
        // 发送POST请求并获取响应
        const response = await apiClientInstance.post(
            `/data_packages/${id}/download`,
            null,
            {
                responseType: 'blob', // 设置响应类型为blob，以接收文件流
            }
        );

        // 返回响应数据（Blob对象）
        return response.data;
    } catch (error) {
        console.error('下载文件流失败:', error);
        throw error; // 抛出错误，由调用者处理
    }
};

// 获取文件
export const fetchGetBlade2D3DFile = async (id, type) => {
    // let data;
    // if (type === '2D图纸') {
    //   data = {
    //     code: '820203',
    //     // url: null,
    //     url: 'http://turbine-api.aialign.com.cn/media/files/123.ppt',
    //     status: '解析中',
    //     message: '解析成功',
    //   };
    // } else if (type == '3D模型') {
    //   data = {
    //     code: '820203',
    //     url: 'http://turbine-api.aialign.com.cn/media/files/jt/7N24_23_02_01_hole_.jt',
    //     status: '解析成功',
    //     message: '解析成功',
    //   };
    // }
    // console.log('data', data, '============');
    // return { data: data };
    return apiClientInstance.get(`/blades/${id}/file`, { params: { type } });
};

// 问答精准度校验接口
export const validateAnswerAccuracyApi = async (
    standardAnswer,
    actualAnswer
) => {
    try {
        debugger;
        const response = await fetch(
            'http://idtest.aialign.com.cn/agentapp/v1/remote-agent-invoke',
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    standard_answer: standardAnswer,
                    actual_answer: actualAnswer,

                    stream: 0,
                    token: 'turbine-test-precision',
                    task_message: '你好,吃饭了吗',
                    return_type: 'response_obj',
                }),
            }
        );

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.text();
        return result === 'true';
    } catch (error) {
        console.error('答案精准度校验失败:', error);
        throw error;
    }
};
