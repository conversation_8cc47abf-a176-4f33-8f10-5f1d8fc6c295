# 问答精准度测试页面

## 功能概述

问答精准度测试页面是一个专门用于测试AI应用问答准确性的工具页面。它可以批量测试多个问题，并自动校验AI回答与标准答案的精准度。

## 页面路由

访问路径：`/accuracy-test`

## 主要功能

### 1. 应用选择
- 支持从应用列表中选择要测试的AI应用
- 使用 `getAllAppsApi` 接口获取可用应用列表

### 2. 测试问题配置
- 支持JSON格式的批量问题输入
- 每个问题包含 `question`（问题）和 `answer`（标准答案）字段

### 3. 自动化测试流程
- 逐个向AI应用发送问题
- 使用流模式接收AI回答
- 自动调用精准度校验接口验证答案准确性
- 实时显示测试进度和结果

### 4. 结果统计与展示
- 显示每个问题的测试结果（正确/错误）
- 统计总体精准度百分比
- 详细展示问题、标准答案、AI回答的对比

## 使用方法

### 1. 准备测试数据

测试问题需要按照以下JSON格式准备：

```json
[
    {
        "question": "7N24.23.01.01的零件名称是什么?",
        "answer": "高压第1级动叶(左旋)"
    },
    {
        "question": "7N24.23.01.01这个零件的具体名称是什么?",
        "answer": "高压第1级动叶(左旋)"
    },
    {
        "question": "7N24.23.01.01的零件图号是什么?",
        "answer": "7N24.23.01.01"
    }
]
```

### 2. 操作步骤

1. **选择测试应用**：从下拉列表中选择要测试的AI应用
2. **输入测试问题**：在文本框中粘贴JSON格式的测试问题
3. **启动测试**：点击"启动测试"按钮开始自动化测试
4. **查看结果**：测试完成后查看精准度统计和详细结果

### 3. 测试过程监控

- **实时进度**：显示当前测试进度条和问题序号
- **当前问题**：显示正在测试的问题和AI回答过程
- **停止测试**：可随时中断正在进行的测试

## 技术实现

### 核心API接口

1. **sendTaskmessageApi**：发送问题到AI应用，使用流模式接收回答
2. **validateAnswerAccuracyApi**：校验AI回答与标准答案的精准度
3. **getAllAppsApi**：获取可用的AI应用列表

### 流数据处理

- 使用 `parseStreamData` 和 `parseStreamToJson` 函数处理流式响应
- 实时累积AI回答内容直到完整接收
- 支持测试中断和错误处理

### 精准度校验

- 调用外部校验接口：`http://idtest.aialign.com.cn/agentapp/v1/remote-agent-invoke`
- 传入标准答案和AI实际回答
- 返回 `true`/`false` 表示是否准确

## 页面布局

### 左侧配置区域（400px宽）
- 应用选择下拉框
- 测试问题JSON输入框
- 启动/停止测试按钮
- 测试进度显示
- 结果统计面板

### 右侧展示区域
- 测试过程实时显示
- 历史测试结果列表
- 每个问题的详细对比信息

## 样式特性

- 使用 Tailwind CSS 进行样式设计
- 响应式布局适配不同屏幕尺寸
- 渐变背景和现代化UI设计
- 清晰的状态指示和进度反馈

## 错误处理

- JSON格式验证
- 网络请求异常处理
- 测试中断处理
- 应用权限验证

## 注意事项

1. **JSON格式**：测试问题必须严格按照指定的JSON格式
2. **应用权限**：确保选择的应用有访问权限
3. **网络连接**：需要稳定的网络连接进行流式数据传输
4. **测试时间**：大批量测试可能需要较长时间，请耐心等待

## 扩展功能

未来可以考虑添加的功能：
- 测试结果导出（Excel/CSV）
- 测试模板保存和加载
- 批量应用测试
- 测试历史记录
- 更详细的错误分析
